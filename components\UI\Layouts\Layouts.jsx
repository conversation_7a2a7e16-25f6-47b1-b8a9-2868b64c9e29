import SideNavbar from '../SideNavbar/SideNavbar'
import Footer from '../Footer/Footer'
import Navbar from '../Navbar/Navbar'
import { ConditionalDisplay } from '../ConditionalDisplay/ConditionalDisplay'
import { LayoutContextProvider } from './LayoutContextProvider'
import styles from './Layout.module.css'
import { AiChat } from '../../FormBuilder/Settings/AiChat/AiChat'
import { useAxiosInterceptor } from '../../../api/useAxiosInterceptor'

export default function Layouts({ children, router }) {
  const sideNavbarExclude = [
    '/update/',
    '/view',
    '/document/upload',
    '/document/upload/[id]',
    '/esignature/uploadOne',
    '/esignature/signOne',
    '/esignature/signTwo',
    '/InboxScanner/inboxScannerOne',
    '/document/bulkUpload',
    '/esignature/residency',
    '/esignature/request/residency',
    '/PurchaseOrder/[id]',
    '/Poilcy-and-procedures/[id]',
    '/Supplier/[id]',
    '/test',
    '/Queries/',
    '/Query/[id]',
    '/FileRoutingUpdate',
    '/dms/UploadDocument',
    '/dms/InboxMonitoring/ViewHistory',
    '/LeadGeneration/LeadForm',
    '/LeadGeneration/EnrollmentForm',
    '/LeadGeneration/FeePaymentForm',
    '/LeadGeneration/PaymentSuccessfulForm'
  ]

  const navbarExclude = [
    '/preview',
    '/view',
    // "/update/",
    // "PurchaseOrder/[id]", // I uncommented this because it was duplicating the navbar in PurchaseOrder form builder page.
    '/Poilcy-and-procedures/[id]'
    // "/Supplier/[id]"
  ]

  // We need to refactor on how we handle the navbar exceptions and maybe the sideNavbar exceptions. We use the NavBar components by itself in the view page and this causes the navbar to not show up on
  // other pages. We need to refactor this to be more dynamic and not hardcoded. - Yibran
  const navbarExceptions = ['/Queries/view/']

  const shouldExclude = (pathname, excludeList, exceptionList) => {
    const isException = exceptionList.some((path) => pathname.startsWith(path))
    if (isException) return false

    return excludeList.some((path) => pathname.includes(path))
  }

  useEffect(() => {
    async function handleAuth() {
      const tokens = await oktaAuth.token.parseFromUrl()
      oktaAuth.tokenManager.setTokens(tokens.tokens)
    }
    handleAuth()
  }, [])

  const footerExclude = [
    '/update/',
    '/document/upload',
    '/document/upload/[id]',
    '/esignature/uploadOne',
    '/esignature/signOne',
    '/esignature/signTwo',
    '/InboxScanner/inboxScannerOne',
    '/document/bulkUpload',
    '/esignature/residency',
    '/PurchaseOrder/[id]',
    '/Poilcy-and-procedures/[id]',
    '/Supplier/[id]'
  ]

  useAxiosInterceptor()

  return (
    <LayoutContextProvider>
      <ConditionalDisplay condition={!shouldExclude(router.pathname, navbarExclude, navbarExceptions)}>
        <Navbar router={router} />
      </ConditionalDisplay>
      <Container removeTopPadding={shouldExclude(router.pathname, navbarExclude, navbarExceptions)} pathName={router.pathname}>
        <ConditionalDisplay condition={!shouldExclude(router.pathname, sideNavbarExclude, [])}>
          <SideNavbar />
        </ConditionalDisplay>
        {children}
      </Container>
      {/* We should remove the Footer from here and instead put the pages that need this component directly in the page's index.js file */}
      {/* <ConditionalDisplay condition={!footerExclude.some((path) => router.pathname.includes(path))}>
       <Footer />
      </ConditionalDisplay> */}
      {/* <AiChat /> */}
    </LayoutContextProvider>
  )
}

export const Container = ({ children, removeTopPadding, pathName }) => {
  const isLeadGenerationPath = pathName?.includes('LeadGeneration')
  const isLMS = pathName?.includes('LMS' || 'InstructorLMS')
  return (
    <div
      className={`${isLMS ? styles.LMSContainer : isLeadGenerationPath ? styles.metronicContainer : styles.container} ${
        removeTopPadding ? styles.removePadding : ''
      }`}
    >
      {children}
    </div>
  )
}
