import { useState } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/router'
import RevvdTitle from '../../../public/LoginScreen/Revvdd.svg'
import Illustration from '../../../public/LoginScreen/illustration.svg'
import IllustrationITSM from '../../../public/LoginScreen/itsm_login_image.svg'
import BusinessIdLogo from '../../../public/LoginScreen/itsm_business_id_logo.png'
import BusinessEmailLogo from '../../../public/LoginScreen/itsm_business_email_logo.png'
import AdminNotificationLogo from '../../../public/LoginScreen/itsm_admin_notification_logo.png'
import PrimaryButton from '../PrimaryButton/PrimaryButton'
import TextInput from '../Input/TextInput/TextInput'
import { InputSwitch } from 'primereact/inputswitch'
import styles from './SignInPage.module.css'
import { ConditionalDisplay } from '../ConditionalDisplay/ConditionalDisplay'
import clsx from 'clsx'
import { InputOtp } from 'primereact/inputotp'
import SecondaryButton from '../SecondaryButton/SecondaryButton'

const loginImage = process.env.NEXT_PUBLIC_LOGIN_IMAGE
const imageName = process.env.NEXT_PUBLIC_IMAGE_NAME
const imagePath = require(`../../../public/LoginScreen/${imageName}`)
const loginImagePath = require(`../../../public/LoginScreen/${loginImage}`)

export const SignInPage = ({ handleSignInClick }) => {
  const router = useRouter()
  const { pathname } = router

  // Check if the URL contains /ITSM/
  const isITSM = pathname.includes('/ITSM')

  return (
    <div className={styles.container}>
      <LeftSection isITSM={isITSM} />
      <RightSection isITSM={isITSM} imagePath={imagePath} loginImagePath={loginImagePath} handleSignInClick={handleSignInClick} />
    </div>
  )
}

const LeftSection = ({ isITSM = false }) => {
  return (
    <div className={styles.leftSection}>
      <div className={styles.illustrationWrapper}>
        <Image
          // className={styles.illustration}
          src={isITSM ? IllustrationITSM : Illustration}
          alt="Person with documents"
          width={isITSM ? 924 : 538}
          height={isITSM ? 543 : 538}
        />
      </div>
      <ConditionalDisplay condition={!isITSM}>
        <div className={styles.textWrapper}>
          <span style={{ textAlign: 'left' }}>
            <p>Your Connected campus, Powered by {process.env.NEXT_PUBLIC_APP_NAME}!</p>
          </span>
        </div>
      </ConditionalDisplay>
    </div>
  )
}

const RightSection = ({ isITSM = false, imagePath, loginImagePath, handleSignInClick }) => {
  return (
    <div className={styles.rightSection} style={isITSM ? { justifyContent: 'center' } : null}>
      {' '}
      {/* This is needed to vertically center ITSMLoginForm */}
      <ConditionalDisplay condition={!isITSM}>
        <div className={styles.flowerLogoWrapper}>
          <Image className={styles.flowerLogo} src={loginImagePath} alt="Login Logo" />
        </div>
      </ConditionalDisplay>
      <BrandWrapper imagePath={imagePath} handleSignInClick={handleSignInClick} isITSM={isITSM} />
      <ConditionalDisplay condition={!isITSM}>
        <div className={styles.footerWrapper}>
          <footer className={styles.footer}>
            <div className={styles.footerLinks}>
              <a
                className={styles.link}
                href={`https://www.${process.env.NEXT_PUBLIC_APP_NAME}.com/#about`}
                target="_blank"
                rel="noreferrer"
              >
                About Us
              </a>
              <span className={styles.separator}>|</span>
              <a
                className={styles.link}
                href={`https://www.${process.env.NEXT_PUBLIC_APP_NAME}.com/#contactUs`}
                target="_blank"
                rel="noreferrer"
              >
                Contact Us
              </a>
            </div>
            <div className={styles.copyright}>
              {process.env.NEXT_PUBLIC_APP_NAME}, © 2025 {process.env.NEXT_PUBLIC_COMPANY_NAME} - All Rights Reserved.
            </div>
          </footer>
        </div>
      </ConditionalDisplay>
    </div>
  )
}

const BrandWrapper = ({ imagePath, handleSignInClick, isITSM = false }) => {
  return (
    <div className={styles.brandWrapper}>
      <div className="flex mb-7 align-items-center flex-column">
        <ConditionalDisplay condition={process.env.NEXT_PUBLIC_APP_NAME !== 'Papyrus' && !isITSM}>
          <Image className={clsx('mb-2', styles.titleLogo)} src={RevvdTitle} alt="Revvd Logo" width={100} />
        </ConditionalDisplay>
        <Image
          className={styles.titleLogo}
          src={imagePath}
          alt={`${process.env.NEXT_PUBLIC_APP_NAME}logo`}
          width={isITSM ? 717 : 354}
          height={isITSM ? 169 : 64}
        />
      </div>
      {isITSM ? (
        <ItsmLoginForm handleSignInClick={handleSignInClick} />
      ) : (
        <PrimaryButton text="Sign in" label="Sign in" onClick={handleSignInClick} />
      )}
    </div>
  )
}

const ItsmLoginForm = ({ handleSignInClick }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [rememberMe, setRememberMe] = useState(false)

  const [isRegistering, setIsRegistering] = useState(false)
  const [isBusinessEmail, setIsBusinessEmail] = useState(false)
  const [isAdminNotified, setIsAdminNotified] = useState(false)

  const showUserLogin = !isRegistering && !isBusinessEmail && !isAdminNotified

  const handleLogin = (e) => {
    e.preventDefault()
    // Handle login logic here
    handleSignInClick()
  }

  return (
    <>
      <ConditionalDisplay condition={showUserLogin}>
        <UserLogin
          email={email}
          setEmail={setEmail}
          password={password}
          setPassword={setPassword}
          rememberMe={rememberMe}
          setRememberMe={setRememberMe}
          handleLogin={handleLogin}
          setIsRegistering={setIsRegistering}
        />
      </ConditionalDisplay>
      <ConditionalDisplay condition={isRegistering}>
        <BusinessIdOTP setIsRegistering={setIsRegistering} setIsBusinessEmail={setIsBusinessEmail} />
      </ConditionalDisplay>
      <ConditionalDisplay condition={isBusinessEmail}>
        <BusinessEmailForm
          setIsRegistering={setIsRegistering}
          setIsBusinessEmail={setIsBusinessEmail}
          setIsAdminNotified={setIsAdminNotified}
        />
      </ConditionalDisplay>
      <ConditionalDisplay condition={isAdminNotified}>
        <AdminNotificationScreen setIsRegistering={setIsRegistering} setIsAdminNotified={setIsAdminNotified} />
      </ConditionalDisplay>
    </>
  )
}

const UserLogin = ({ email, setEmail, password, setPassword, rememberMe, setRememberMe, handleLogin, setIsRegistering }) => {
  return (
    <div className={styles.loginContainer}>
      <TextInput
        id="email"
        label="Login"
        placeholder="Enter Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        className={styles.itsmInputField}
        theme="metronic"
      />

      {/* TODO: This needs to be updated to an InputMask component so the user cannot see the input like typical password fields. */}
      <TextInput
        id="password"
        label="Password"
        placeholder="Enter Password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        className={styles.itsmInputField}
        theme="metronic"
        icon="pi pi-eye"
      />

      <div className={styles.formOptions}>
        <div className={styles.rememberMe}>
          <InputSwitch checked={rememberMe} onChange={(e) => setRememberMe(e.value)} />
          <span className={styles.rememberMeText}>Remember Me</span>
        </div>
        <a href="#" className={styles.forgotPassword}>
          Forgot Password?
        </a>
      </div>

      <div>
        <PrimaryButton
          text="Login"
          onClick={handleLogin}
          width="717"
          height={50}
          fontSize={16}
          buttonstyle={{ backgroundColor: "#1b84ff'" }}
        />
      </div>

      <div className={styles.registerLink}>
        <span>No Account?</span>
        <span className={styles.colorLink} onClick={() => setIsRegistering(true)}>
          Register
        </span>
      </div>
    </div>
  )
}

const BusinessIdOTP = ({ setIsRegistering, setIsBusinessEmail }) => {
  const [tokens, setTokens] = useState('')

  return (
    <div className={styles.loginContainer}>
      <h1 style={{ margin: '-45px 0 0 0' }}>Verify your Organization with a Business ID</h1>
      <Image src={BusinessIdLogo} alt="Business ID Logo" width={322} height={324} />
      <h3 style={{ margin: '0' }}>Business ID</h3>
      <InputOtp length={3} style={{ width: '300px' }} value={tokens} onChange={(e) => setTokens(e.value)} />
      <PrimaryButton
        text="Continue"
        width={717}
        disabled={tokens.length < 3} // Three is the max digits for the business ID (as of the time of this writing).
        onClick={() => {
          setIsRegistering(false)
          setIsBusinessEmail(true)
        }}
        buttonstyle={{ backgroundColor: "#1b84ff'" }}
      />
      <div className={styles.registerLink}>
        <span className={styles.colorLink} onClick={() => setIsRegistering(false)}>
          Cancel
        </span>
      </div>
    </div>
  )
}

const BusinessEmailForm = ({ setIsRegistering, setIsBusinessEmail, setIsAdminNotified }) => {
  const [businessEmail, setBusinessEmail] = useState('')

  return (
    <div className={styles.loginContainer}>
      <h1 style={{ margin: '-45px 0 0 0' }}>Provide your Business Email Address</h1>
      <Image src={BusinessEmailLogo} alt="Business Email Logo" width={322} height={324} />
      <TextInput
        id="businessEmail"
        label="Email"
        placeholder="Enter Business Email"
        value={businessEmail}
        onChange={(e) => setBusinessEmail(e.target.value)}
        className={styles.itsmInputField}
        theme="metronic"
      />
      <SecondaryButton
        text="Submit"
        width={717}
        disabled={businessEmail.length === 0}
        onClick={() => {
          setIsAdminNotified(true), setIsBusinessEmail(false)
        }}
      />
      <div className={styles.registerLink}>
        <span
          className={styles.colorLink}
          onClick={() => {
            setIsRegistering(false), setIsBusinessEmail(false)
          }}
        >
          Cancel
        </span>
      </div>
    </div>
  )
}

const AdminNotificationScreen = ({ setIsRegistering, setIsAdminNotified }) => {
  return (
    <div className={styles.adminNotificationContainer}>
      <h1 className={styles.adminNotificationTitle}>We Have Notified Your System Admin</h1>
      <Image className={styles.adminNotificationImage} src={AdminNotificationLogo} alt="Admin Notification Logo" width={322} height={324} />
      <p className={styles.adminNotificationDescription}>
        We`&lsquo;`ve sent your request to your organization`&lsquo;`s admin for approval
      </p>
      <PrimaryButton
        text="Return To Login"
        width={717}
        onClick={() => setIsAdminNotified(false)}
        buttonstyle={{ backgroundColor: "#1b84ff'" }}
      />
      <div className={styles.registerLink}>
        <span
          className={styles.colorLink}
          onClick={() => {
            setIsAdminNotified(false), setIsRegistering(false)
          }}
        >
          Cancel
        </span>
      </div>
    </div>
  )
}

// TODO: This component needs to be implemented once the backend for story 4592 is completed
const SetPasswordScreen = ({ setIsRegistering, setIsAdminNotified }) => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  const handlePasswordSubmit = (e) => {
    e.preventDefault()
    // Handle password setting logic here
    // This will be implemented when backend is ready
  }

  return (
    <div className={styles.loginContainer}>
      <h1 style={{ margin: '-45px 0 0 0' }}>Please Set Your Password</h1>
      <Image src={IllustrationITSM} alt="Password Setup Illustration" width={322} height={324} />
      <TextInput
        id="password"
        label="Enter Your Password:"
        placeholder="Please Enter Your Password..."
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        className={styles.itsmInputField}
        theme="metronic"
      />
      <TextInput
        id="confirmPassword"
        label="Re-enter Your Password:"
        placeholder="Confirm Your Password..."
        value={confirmPassword}
        onChange={(e) => setConfirmPassword(e.target.value)}
        className={styles.itsmInputField}
        theme="metronic"
      />
      <PrimaryButton
        text="Submit"
        width={717}
        disabled={password.length === 0 || confirmPassword.length === 0 || password !== confirmPassword}
        onClick={handlePasswordSubmit}
        buttonstyle={{ backgroundColor: "#1b84ff'" }}
      />
      <div className={styles.registerLink}>
        <span
          className={styles.colorLink}
          onClick={() => {
            setIsAdminNotified(false), setIsRegistering(false)
          }}
        >
          Cancel
        </span>
      </div>
    </div>
  )
}
